#!/usr/bin/env python3

"""
Test script to verify that dropdown menu items can be clicked properly
after implementing the delayed hide fix.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fabric.widgets.button import Button
from fabric.widgets.box import Box
from fabric.widgets.label import Label
from fabric.widgets.centerbox import CenterBox
from fabric.application import Application
from widgets.dropdown import ModusDropdown
from widgets.mousecapture import DropDownMouseCapture
from widgets.wayland import WaylandWindow
from utils.service import modus_service

def test_dropdown_option(label: str = "", on_click_msg: str = ""):
    """Create a test dropdown option that prints a message when clicked"""
    def on_click_test(button):
        print(f"✅ Menu item clicked successfully: {on_click_msg}")
        modus_service.current_dropdown = -1
    
    return Button(
        child=CenterBox(
            start_children=[
                Label(label=label, h_align="start", name="dropdown-option-label"),
            ],
            orientation="horizontal",
            h_align="fill",
            h_expand=True,
            v_expand=True,
        ),
        name="dropdown-option",
        h_align="fill",
        on_clicked=on_click_test,
        h_expand=True,
        v_expand=True,
    )

class TestWindow(WaylandWindow):
    def __init__(self):
        super().__init__(
            name="test-dropdown",
            layer="top",
            anchor="center",
            exclusivity="auto",
            visible=True,
        )
        
        # Create test dropdown
        self.test_dropdown = ModusDropdown(
            dropdown_id="test-dropdown",
            parent=self,
            dropdown_children=[
                test_dropdown_option("Test Item 1", "Item 1 was clicked!"),
                test_dropdown_option("Test Item 2", "Item 2 was clicked!"),
                test_dropdown_option("Test Item 3", "Item 3 was clicked!"),
            ],
        )
        
        # Create mouse capture for dropdown
        self.dropdown_capture = DropDownMouseCapture(
            layer="top", 
            child_window=self.test_dropdown
        )
        
        # Create button to show dropdown
        self.test_button = Button(
            label="Click to Test Dropdown",
            name="test-button",
            on_clicked=lambda _: self.dropdown_capture.toggle_mousecapture(),
        )
        
        self.test_dropdown.set_pointing_to(self.test_button)
        
        self.children = [Box(
            children=[
                Label(label="Dropdown Click Test", name="test-title"),
                self.test_button,
                Label(label="Click the button above, then click menu items", name="test-instructions"),
            ],
            orientation="vertical",
            spacing=10,
        )]

def main():
    print("🧪 Testing dropdown menu item clicks...")
    print("📋 Instructions:")
    print("   1. Click the 'Click to Test Dropdown' button")
    print("   2. Click on any menu item")
    print("   3. Check if the success message appears in console")
    print("   4. Press Ctrl+C to exit")
    print()
    
    app = Application("test-dropdown-fix")
    window = TestWindow()
    app.add_window(window)
    app.run()

if __name__ == "__main__":
    main()
