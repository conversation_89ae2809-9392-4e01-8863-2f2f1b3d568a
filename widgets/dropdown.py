from fabric.widgets.box import Box
from fabric.widgets.centerbox import CenterBox
from fabric.widgets.eventbox import EventBox

from utils.service import modus_service
from widgets.popup_window import PopupWindow

dropdowns = []


def dropdown_divider(comment):
    return Box(
        children=[Box(name="dropdown-divider", h_expand=True)],
        name="dropdown-divider-box",
        h_align="fill",
        h_expand=True,
        v_expand=True,
    )


class ModusDropdown(PopupWindow):
    def __init__(self, dropdown_children=None, dropdown_id=None, **kwargs):
        super().__init__(
            layer="top",
            exclusivity="auto",
            name="dropdown-menu",
            keyboard_mode="none",
            visible=False,
            **kwargs,
        )

        self.id = dropdown_id or str(len(dropdowns))
        dropdowns.append(self)

        modus_service.connect("dropdowns-hide-changed", self.hide_dropdown)

        self.dropdown = Box(
            children=dropdown_children or [],
            h_expand=True,
            name="dropdown-options",
            orientation="vertical",
        )

        self.child_box = CenterBox(start_children=[self.dropdown])

        self.event_box = EventBox(
            events=["enter-notify-event", "leave-notify-event"],
            child=self.child_box,
            all_visible=True,
        )

        self.children = [self.event_box]
        self.connect("button-press-event", self.hide_dropdown)
        self.add_keybinding("Escape", self.hide_dropdown)

    def toggle_dropdown(self, button, parent=None):
        self.set_visible(not self.is_visible())
        modus_service.current_dropdown = self.id if self.is_visible() else None

    def hide_dropdown(self, widget, event):
        # Handle service-triggered hide events (when switching between dropdowns)
        if str(modus_service.current_dropdown) != str(self.id) and self.is_visible():
            self.hide()
            return

        # Handle button-press-event - only hide if clicking outside dropdown content
        if hasattr(event, 'button') and event.button == 1:  # Left click
            # Get click coordinates relative to the dropdown window
            click_x, click_y = event.x, event.y

            # Get the dropdown content area bounds
            dropdown_allocation = self.dropdown.get_allocation()

            # Check if click is within the dropdown content bounds
            if (dropdown_allocation.x <= click_x <= dropdown_allocation.x + dropdown_allocation.width and
                dropdown_allocation.y <= click_y <= dropdown_allocation.y + dropdown_allocation.height):
                # Click is inside dropdown content, don't hide
                return

            # Click is outside dropdown content, hide the dropdown
            self.hide()

    def _set_mousecapture(self, visible: bool) -> None:
        self.set_visible(visible)
        if visible:
            modus_service.current_dropdown = self.id

    def on_cursor_enter(self, *_):
        self.set_visible(True)

    def on_cursor_leave(self, *_):
        if self.is_hovered():
            return
        self.set_visible(False)
        modus_service.dropdowns_hide = not modus_service.dropdowns_hide
